package goresodownload

import (
	"sync"
	"testing"
	"time"
)

// TestGracefulShutdown tests that the downloader gracefully shuts down
// without leaving hanging goroutines or incomplete tasks
func TestGracefulShutdown(t *testing.T) {
	// Create a downloader with minimal configuration
	config := NewDefaultConfig()
	config.DownloadConcurrency = 2
	config.DeleteConcurrency = 1
	
	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/tmp/test_downloads"},
	})
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create some test tasks
	tasks := []MediaTask{
		{
			URL:      "https://httpbin.org/delay/2", // 2 second delay
			DestPath: "test1.jpg",
			IsPhoto:  true,
		},
		{
			URL:      "https://httpbin.org/delay/3", // 3 second delay
			DestPath: "test2.jpg",
			IsPhoto:  true,
		},
	}

	// Start processing tasks in a goroutine
	var wg sync.WaitGroup
	var processingErr error
	
	wg.Add(1)
	go func() {
		defer wg.Done()
		
		// Simulate processing analysis result
		result := &AnalysisResult{
			ID:            "test-prop-123",
			DownloadTasks: tasks,
			DeleteTasks:   []DeleteTask{},
		}
		
		_, err := downloader.ProcessAnalysisResult(result, "TEST")
		processingErr = err
	}()

	// Wait a bit to let the downloads start
	time.Sleep(500 * time.Millisecond)

	// Now stop the downloader (simulating graceful shutdown)
	t.Log("Stopping downloader...")
	downloader.Stop()
	t.Log("Downloader stopped")

	// Wait for the processing to complete
	wg.Wait()

	// Check that processing completed (even if with errors due to cancellation)
	if processingErr != nil {
		t.Logf("Processing completed with error (expected): %v", processingErr)
	} else {
		t.Log("Processing completed successfully")
	}

	t.Log("Test completed - no hanging goroutines")
}

// TestWorkerPoolDraining tests that worker pool properly drains jobs on shutdown
func TestWorkerPoolDraining(t *testing.T) {
	// Create a worker pool
	config := NewDefaultConfig()
	downloader := &Downloader{
		DownloaderOptions: &DownloaderOptions{
			Config:       config,
			StoragePaths: []string{"/tmp/test"},
		},
	}

	wp := NewWorkerPool(downloader, 1, 1)

	// Create a job that will be submitted
	resultChan := make(chan error, 1)
	job := DownloadJob{
		Task: MediaTask{
			URL:      "https://httpbin.org/delay/1",
			DestPath: "test.jpg",
			IsPhoto:  true,
		},
		PropID: "test-prop",
		Result: resultChan,
	}

	// Submit the job
	wp.downloadChan <- job

	// Immediately stop the worker pool
	go func() {
		time.Sleep(100 * time.Millisecond)
		wp.Stop()
	}()

	// Wait for the result - this should not hang
	select {
	case err := <-resultChan:
		t.Logf("Received result: %v", err)
	case <-time.After(10 * time.Second):
		t.Fatal("Timeout waiting for result - worker pool did not drain properly")
	}
}
