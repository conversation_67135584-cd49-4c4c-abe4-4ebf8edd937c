package goresodownload

import (
	"context"
	"fmt"
	"image/jpeg"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/gofile"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Download processing constants
const (
	// Thumbnail dimensions
	THUMBNAIL_WIDTH  = 240 // Thumbnail width in pixels
	THUMBNAIL_HEIGHT = 160 // Thumbnail height in pixels

	// Error channel buffer size for concurrent processing
	ErrorChannelBufferSize = 2

	// Image compression settings
	DefaultTargetSizeKB = 300 // Target size for image compression in KB

	// File system permissions
	DefaultDirPermissions = 0755 // Standard directory permissions (rwxr-xr-x)
)

// FailedTask represents a failed download task in the database
type FailedTask struct {
	ID         primitive.ObjectID `bson:"_id,omitempty"`
	PropID     string             `bson:"prop_id"`
	ImageURL   string             `bson:"image_url"`
	ErrMsg     string             `bson:"err_msg"`
	RetryCount int                `bson:"retry_count"`
}

// DownloaderConfig represents the configuration for the downloader
type DownloaderConfig struct {
	// Concurrency settings
	DownloadConcurrency int `json:"download_concurrency"`
	DeleteConcurrency   int `json:"delete_concurrency"`

	// Retry settings
	MaxRetries int `json:"max_retries"`

	// Alert settings
	ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // Default: 3
}

// NewDefaultConfig returns a new DownloaderConfig with default values
func NewDefaultConfig() *DownloaderConfig {
	return &DownloaderConfig{
		DownloadConcurrency:          5, // Restored to 5 as requested
		DeleteConcurrency:            5, // Restored to 5 for consistency
		MaxRetries:                   3,
		ConsecutiveFailuresThreshold: 3,
	}
}

// DownloaderOptions contains all options for creating a Downloader
type DownloaderOptions struct {
	Config       *DownloaderConfig
	StoragePaths []string
	MergedCol    *gomongo.MongoCollection
	FailedCol    *gomongo.MongoCollection
}

// WorkerPool manages a fixed number of workers for downloads and deletions
type WorkerPool struct {
	downloader      *Downloader // Reference to parent downloader
	downloadWorkers int
	deleteWorkers   int
	downloadChan    chan DownloadJob
	deleteChan      chan DeleteJob
	wg              sync.WaitGroup
	ctx             context.Context
	cancel          context.CancelFunc
}

// DownloadJob represents a download task with context
type DownloadJob struct {
	Task   MediaTask
	PropID string
	Result chan error
}

// DeleteJob represents a delete task with context
type DeleteJob struct {
	Task   DeleteTask
	Result chan error
}

// Downloader implements the media download functionality
type Downloader struct {
	*DownloaderOptions
	workerPool *WorkerPool
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(downloader *Downloader, downloadWorkers, deleteWorkers int) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())

	wp := &WorkerPool{
		downloader:      downloader,
		downloadWorkers: downloadWorkers,
		deleteWorkers:   deleteWorkers,
		downloadChan:    make(chan DownloadJob, downloadWorkers*2), // Buffer for smooth operation
		deleteChan:      make(chan DeleteJob, deleteWorkers*2),
		ctx:             ctx,
		cancel:          cancel,
	}

	wp.start()
	return wp
}

// start initializes and starts the worker pool
func (wp *WorkerPool) start() {
	golog.Info("Starting worker pool", "downloadWorkers", wp.downloadWorkers, "deleteWorkers", wp.deleteWorkers)

	// Start download workers
	for i := 0; i < wp.downloadWorkers; i++ {
		wp.wg.Add(1)
		go wp.downloadWorker(i)
	}

	// Start delete workers
	for i := 0; i < wp.deleteWorkers; i++ {
		wp.wg.Add(1)
		go wp.deleteWorker(i)
	}
}

// downloadWorker processes download jobs
func (wp *WorkerPool) downloadWorker(workerID int) {
	defer wp.wg.Done()
	golog.Debug("Download worker started", "workerID", workerID)

	for {
		select {
		case job, ok := <-wp.downloadChan:
			if !ok {
				golog.Debug("Download worker stopping - channel closed", "workerID", workerID)
				return
			}

			// Process the download job with panic recovery
			func() {
				defer func() {
					if r := recover(); r != nil {
						golog.Error("Panic in download worker", "workerID", workerID, "propId", job.PropID, "panic", r)
						job.Result <- fmt.Errorf("panic in download worker: %v", r)
					}
				}()
				err := wp.processDownloadJob(job)
				job.Result <- err
			}()

		case <-wp.ctx.Done():
			golog.Debug("Download worker cancelled by context", "workerID", workerID)
			// When context is cancelled, we should still try to drain any remaining jobs
			// to avoid leaving callers hanging
			for {
				select {
				case job, ok := <-wp.downloadChan:
					if !ok {
						golog.Debug("Download worker finished draining channel", "workerID", workerID)
						return
					}
					// Send cancellation error to avoid hanging callers
					job.Result <- fmt.Errorf("download cancelled due to shutdown")
				default:
					golog.Debug("Download worker no more jobs to drain", "workerID", workerID)
					return
				}
			}
		}
	}
}

// deleteWorker processes delete jobs
func (wp *WorkerPool) deleteWorker(workerID int) {
	defer wp.wg.Done()
	golog.Debug("Delete worker started", "workerID", workerID)

	for {
		select {
		case job, ok := <-wp.deleteChan:
			if !ok {
				golog.Debug("Delete worker stopping - channel closed", "workerID", workerID)
				return
			}

			// Process the delete job with panic recovery
			func() {
				defer func() {
					if r := recover(); r != nil {
						golog.Error("Panic in delete worker", "workerID", workerID, "panic", r)
						job.Result <- fmt.Errorf("panic in delete worker: %v", r)
					}
				}()
				err := wp.processDeleteJob(job)
				job.Result <- err
			}()

		case <-wp.ctx.Done():
			golog.Debug("Delete worker cancelled by context", "workerID", workerID)
			// When context is cancelled, we should still try to drain any remaining jobs
			// to avoid leaving callers hanging
			for {
				select {
				case job, ok := <-wp.deleteChan:
					if !ok {
						golog.Debug("Delete worker finished draining channel", "workerID", workerID)
						return
					}
					// Send cancellation error to avoid hanging callers
					job.Result <- fmt.Errorf("deletion cancelled due to shutdown")
				default:
					golog.Debug("Delete worker no more jobs to drain", "workerID", workerID)
					return
				}
			}
		}
	}
}

// processDownloadJob processes a single download job
func (wp *WorkerPool) processDownloadJob(job DownloadJob) error {
	// Now we have access to the downloader instance
	golog.Debug("Processing download job", "propID", job.PropID, "task", job.Task.DestPath)
	return wp.downloader.downloadTask(job.Task, job.PropID)
}

// processDeleteJob processes a single delete job
func (wp *WorkerPool) processDeleteJob(job DeleteJob) error {
	// Now we have access to the downloader instance
	golog.Debug("Processing delete job", "task", job.Task.Path)
	return wp.downloader.deleteTask(job.Task)
}

// Stop gracefully shuts down the worker pool
func (wp *WorkerPool) Stop() {
	golog.Info("Stopping worker pool gracefully")

	// First, close the channels to prevent new jobs from being submitted
	close(wp.downloadChan)
	close(wp.deleteChan)

	// Wait for all workers to finish their current jobs
	// Don't cancel the context yet, let workers finish their current tasks
	wp.wg.Wait()

	// Now cancel the context for any remaining cleanup
	wp.cancel()

	golog.Info("Worker pool stopped gracefully")
}

// NewDownloader creates a new Downloader instance with worker pool
func NewDownloader(opts *DownloaderOptions) (*Downloader, error) {
	if opts == nil {
		opts = &DownloaderOptions{
			Config: NewDefaultConfig(),
		}
	}
	if opts.Config == nil {
		opts.Config = NewDefaultConfig()
	}

	// Create downloader first
	downloader := &Downloader{
		DownloaderOptions: opts,
	}

	// Then create worker pool with reference to downloader
	downloader.workerPool = NewWorkerPool(downloader, opts.Config.DownloadConcurrency, opts.Config.DeleteConcurrency)

	return downloader, nil
}

// Stop gracefully shuts down the downloader and its worker pool
func (d *Downloader) Stop() {
	if d.workerPool != nil {
		d.workerPool.Stop()
	}
}

// ProcessAnalysisResult processes the analysis result and performs downloads/deletions
func (d *Downloader) ProcessAnalysisResult(result *AnalysisResult, board string) (tnChangedNum int, err error) {
	var wg sync.WaitGroup
	errChan := make(chan error, ErrorChannelBufferSize) // Buffer for both download and deletion errors

	// Process downloads
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDownloads(result.DownloadTasks, result.ID); err != nil {
			errChan <- fmt.Errorf("download processing failed: %w", err)
		}
	}()

	// Process deletions
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDeletions(result.DeleteTasks); err != nil {
			errChan <- fmt.Errorf("deletion processing failed: %w", err)
		}
	}()

	// Wait for both operations to complete
	wg.Wait()
	close(errChan)

	// Check for errors
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return 0, fmt.Errorf("processing failed: %v", errs)
	}

	// 收集图片下载统计信息
	if err := d.collectPhotoStats(result); err != nil {
		golog.Error("failed to collect photo stats", "error", err, "ID", result.ID)
		// 不因为统计收集失败而中断主流程
	}

	// Generate thumbnail and update document
	var thumbnailHash int32
	if result.NewFirstPic.MediaKey != "" {
		var err error
		thumbnailHash, tnChangedNum, err = d.generateThumbnailAndUpdate(result.NewFirstPic, result.OldTnLH)
		if err != nil {
			golog.Error("failed to generate thumbnail",
				"error", err,
				"mediaKey", result.NewFirstPic.MediaKey,
				"url", result.NewFirstPic.URL,
				"oldTnLH", result.OldTnLH)
		}
	}

	// Update document with all fields
	golog.Debug("ProcessAnalysisResult", "result", result, "thumbnailHash", thumbnailHash)
	golog.Info("Starting to update merged document", "ID", result.ID, "board", board, "thumbnailHash", thumbnailHash)
	updateErr := d.updateMergedDoc(result, thumbnailHash, board)
	if updateErr != nil {
		golog.Error("failed to update merged document", "error", updateErr, "ID", result.ID, "board", board)
		// 记录错误但不立即返回，让调用者决定如何处理
	} else {
		golog.Info("Successfully updated merged document", "ID", result.ID)
	}

	golog.Info("ProcessAnalysisResult finished", "ID", result.ID, "updateError", updateErr != nil)

	// 返回更新错误，让调用者决定如何处理
	if updateErr != nil {
		return tnChangedNum, fmt.Errorf("failed to update merged document: %w", updateErr)
	}
	return tnChangedNum, nil
}

// updateMergedDoc updates the merged document with PhoLH, DocLH and optional thumbnail hash
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32, board string) error {
	if result == nil {
		golog.Error("updateMergedDoc", "result", result)
		return fmt.Errorf("result is nil")
	}

	// Check if document exists
	golog.Info("Checking if document exists in merged collection", "ID", result.ID, "collection", d.MergedCol.Name())
	var doc bson.M
	err := d.MergedCol.FindOne(context.Background(), bson.M{"_id": result.ID}).Decode(&doc)
	if err != nil {
		golog.Error("updateMergedDoc - document not found", "error", err, "ID", result.ID, "collection", d.MergedCol.Name())
		return fmt.Errorf("document not found: %w", err)
	}
	golog.Info("Document found in merged collection", "ID", result.ID)

	update := bson.M{}
	update["$set"] = bson.M{}
	unsetFields := bson.M{}

	golog.Debug("updateMergedDoc", "result", result.DocLH, "thumbnailHash", thumbnailHash, "board", board)

	// Handle PhoLH
	if len(result.PhoLH) > 0 {
		update["$set"].(bson.M)["phoLH"] = result.PhoLH
	} else {
		unsetFields["phoLH"] = ""
	}

	// Handle DocLH
	if len(result.DocLH) > 0 {
		update["$set"].(bson.M)["docLH"] = result.DocLH
	} else {
		unsetFields["docLH"] = ""
	}

	// Add thumbnail hash if available
	if thumbnailHash != 0 {
		update["$set"].(bson.M)["tnLH"] = thumbnailHash
	} else {
		unsetFields["tnLH"] = ""
	}

	// add phoP:'/L1/L2'
	filePath, err := levelStore.GetFullFilePathForProp(result.PropTs, board, result.Sid)
	if err != nil {
		return fmt.Errorf("failed to get full file path for sid %s: %w", result.Sid, err)
	}
	if filePath != "" {
		update["$set"].(bson.M)["phoP"] = filePath
	} else {
		unsetFields["phoP"] = ""
	}

	// 添加图片下载统计字段
	// TODO: 待collectPhotoStats修改后再添加
	// if result.MaxPicSz > 0 {
	// 	update["$set"].(bson.M)["maxPicSz"] = result.MaxPicSz
	// } else {
	// 	unsetFields["maxPicSz"] = ""
	// }

	// if result.AvgPicSz > 0 {
	// 	update["$set"].(bson.M)["avgPicSz"] = result.AvgPicSz
	// } else {
	// 	unsetFields["avgPicSz"] = ""
	// }

	// if result.TotPicSz > 0 {
	// 	update["$set"].(bson.M)["totPicSz"] = result.TotPicSz
	// } else {
	// 	unsetFields["totPicSz"] = ""
	// }

	// if result.PhodlNum > 0 {
	// 	update["$set"].(bson.M)["phodlNum"] = result.PhodlNum
	// } else {
	// 	unsetFields["phodlNum"] = ""
	// }

	// if !result.PhoDl.IsZero() {
	// 	update["$set"].(bson.M)["phoDl"] = result.PhoDl
	// } else {
	// 	unsetFields["phoDl"] = ""
	// }

	// Only add $unset to update if there are fields to unset
	if len(unsetFields) > 0 {
		update["$unset"] = unsetFields
	}

	// Check if there's anything to update
	setFields := update["$set"].(bson.M)
	if len(setFields) == 0 && len(unsetFields) == 0 {
		golog.Debug("updateMergedDoc", "no fields to update")
		return nil
	}

	golog.Info("Executing merged document update", "ID", result.ID, "set", update["$set"], "unset", update["$unset"])

	// Only for test
	// update["$set"].(bson.M)["DownloadTasks"] = result.DownloadTasks
	// update["$set"].(bson.M)["DeleteTasks"] = result.DeleteTasks

	updateResult, err := d.MergedCol.UpdateOne(
		context.Background(),
		bson.M{"_id": result.ID},
		update,
	)
	if err != nil {
		golog.Error("Failed to update merged document", "error", err, "ID", result.ID)
		return err
	}

	golog.Info("Successfully updated merged document",
		"ID", result.ID,
		"matchedCount", updateResult.MatchedCount,
		"modifiedCount", updateResult.ModifiedCount)
	return nil
}

// processDownloads handles the download tasks using global worker pool
func (d *Downloader) processDownloads(tasks []MediaTask, id string) error {
	if len(tasks) == 0 {
		golog.Info("no tasks to download", "id", id)
		return nil
	}

	golog.Info("Submitting download tasks to global worker pool", "taskCount", len(tasks), "propID", id)

	// Create result channels for each task
	results := make([]chan error, len(tasks))
	for i := range results {
		results[i] = make(chan error, 1)
	}

	// Submit all tasks to the global worker pool
	for i, task := range tasks {
		job := DownloadJob{
			Task:   task,
			PropID: id,
			Result: results[i],
		}

		select {
		case d.workerPool.downloadChan <- job:
			// Task submitted successfully
		default:
			// Channel is full, this shouldn't happen with proper buffering
			golog.Debug("Download channel full, waiting", "propID", id)
			d.workerPool.downloadChan <- job // Block until space available
		}
	}

	// Wait for all tasks to complete and collect errors - NEVER SKIP ANY TASK
	var firstErr error
	for i, resultChan := range results {
		// Wait indefinitely for each task - never give up
		err := <-resultChan
		if err != nil && firstErr == nil {
			firstErr = err
			// Improved error logging with more details
			errorMsg := "unknown error"
			if err != nil {
				errorMsg = err.Error()
				if errorMsg == "" {
					errorMsg = fmt.Sprintf("empty error of type %T", err)
				}
			}
			golog.Error("Download task failed",
				"taskIndex", i,
				"propID", id,
				"error", err,
				"errorMsg", errorMsg,
				"errorType", fmt.Sprintf("%T", err))
		}
		golog.Debug("Download task completed", "taskIndex", i, "propID", id, "success", err == nil)
	}

	golog.Info("All download tasks completed", "taskCount", len(tasks), "propID", id)
	return firstErr
}

// processDeletions handles the deletion tasks using global worker pool
func (d *Downloader) processDeletions(tasks []DeleteTask) error {
	if len(tasks) == 0 {
		golog.Debug("no tasks to delete")
		return nil
	}

	golog.Info("Submitting delete tasks to global worker pool", "taskCount", len(tasks))

	// Create result channels for each task
	results := make([]chan error, len(tasks))
	for i := range results {
		results[i] = make(chan error, 1)
	}

	// Submit all tasks to the global worker pool
	for i, task := range tasks {
		job := DeleteJob{
			Task:   task,
			Result: results[i],
		}

		select {
		case d.workerPool.deleteChan <- job:
			// Task submitted successfully
		default:
			// Channel is full, this shouldn't happen with proper buffering
			golog.Warn("Delete channel full, waiting")
			d.workerPool.deleteChan <- job // Block until space available
		}
	}

	// Wait for all tasks to complete and collect errors - NEVER SKIP ANY TASK
	var firstErr error
	for i, resultChan := range results {
		// Wait indefinitely for each task - never give up
		err := <-resultChan
		if err != nil && firstErr == nil {
			firstErr = err
			golog.Error("Delete task failed", "taskIndex", i, "error", err)
		}
		golog.Debug("Delete task completed", "taskIndex", i, "success", err == nil)
	}

	golog.Info("All delete tasks completed", "taskCount", len(tasks))
	return firstErr
}

// downloadTask handles a single download task
func (d *Downloader) downloadTask(task MediaTask, id string) error {
	// Prepare full paths for all storage locations
	var fullPaths []string
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.DestPath)
		fullPaths = append(fullPaths, fullPath)
	}

	// Ensure URL has proper protocol prefix
	url := task.URL
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	golog.Debug("downloading file", "url", url, "savePaths", fullPaths)
	
	// Use atomic file operations to prevent partial downloads
	opts := &gofile.DownloadAndSaveFileOptions{
		URL:            url,
		SavePaths:      fullPaths,
		CompressWebP:   false,
		IsPhoto:        task.IsPhoto,
		MaxRetries:     d.Config.MaxRetries,
		CompressToSize: true,
		TargetSizeKB:   DefaultTargetSizeKB,
		// Add atomic operation flag if supported by gofile
		// AtomicOperation: true,
	}

	// Create a context with timeout for the download operation
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Download and save the file using gofile's internal retry mechanism
	golog.Debug("downloadTask", "opts", opts)
	
	// Use a channel to handle cancellation
	done := make(chan error, 1)
	go func() {
		_, err := gofile.DownloadAndSaveFile(opts)
		done <- err
	}()

	select {
	case err := <-done:
		if err != nil {
			// Clean up any partial files that might have been created
			d.cleanupPartialDownloads(fullPaths)
			
			// Record failed task
			if recordErr := d.recordFailedTask(task, id, err, d.Config.MaxRetries); recordErr != nil {
				golog.Error("failed to record failed task", "error", recordErr)
			}
			return fmt.Errorf("download failed: %w", err)
		}
	case <-ctx.Done():
		// Context cancelled (timeout or shutdown)
		d.cleanupPartialDownloads(fullPaths)
		return fmt.Errorf("download cancelled: %w", ctx.Err())
	}

	golog.Debug("downloadTask", "success", opts.URL, "savePaths", fullPaths)
	return nil
}

// cleanupPartialDownloads removes any partial files that might have been created during failed downloads
func (d *Downloader) cleanupPartialDownloads(paths []string) {
	for _, path := range paths {
		// Check if file exists and remove it
		if _, err := os.Stat(path); err == nil {
			if err := os.Remove(path); err != nil {
				golog.Error("failed to cleanup partial file", "path", path, "error", err)
			} else {
				golog.Info("cleaned up partial file", "path", path)
			}
		}
	}
}

// deleteTask handles a single deletion task
func (d *Downloader) deleteTask(task DeleteTask) error {
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.Path)

		// Check if path exists and is a file
		fileInfo, err := os.Stat(fullPath)
		if err != nil {
			if os.IsNotExist(err) {
				golog.Info("file does not exist, skipping deletion", "path", fullPath)
				continue
			}
			golog.Error("failed to stat file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// Skip if it's a directory
		if fileInfo.IsDir() {
			golog.Info("path is a directory, skipping deletion", "path", fullPath)
			continue
		}

		// Delete the file
		if err := os.Remove(fullPath); err != nil {
			golog.Error("failed to delete file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to delete file %s: %w", fullPath, err)
		}
	}
	return nil
}

// recordFailedTask records a failed download task in the database
func (d *Downloader) recordFailedTask(task MediaTask, id string, err error, retryCount int) error {
	failedTask := FailedTask{
		PropID:     id,
		ImageURL:   task.URL,
		ErrMsg:     err.Error(),
		RetryCount: retryCount,
	}
	if d.FailedCol == nil {
		return fmt.Errorf("FailedCol is nil, cannot record failed task")
	}
	_, err = d.FailedCol.InsertOne(context.Background(), failedTask)
	return err
}

// generateThumbnailAndUpdate generates a thumbnail and returns the hash value
func (d *Downloader) generateThumbnailAndUpdate(task MediaTask, oldTnLH int32) (int32, int, error) {
	var tnChangedNum int
	// Create thumbnail key
	thumbKey := task.MediaKey + "-t"
	sid := task.Sid

	// Generate hash and filename
	hash := levelStore.MurmurToInt32(thumbKey)
	golog.Debug("generateThumbnailAndUpdate", "hash", hash, "oldTnLH", oldTnLH)
	if hash == oldTnLH { // thumbnail not changed
		golog.Info("thumbnail already exists, skipping", "hash", hash)
		return hash, 0, nil
	}
	if oldTnLH != 0 && hash == 0 {
		tnChangedNum = -1
	}
	if oldTnLH == 0 && hash != 0 {
		tnChangedNum = 1
	}

	fileName, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to generate thumbnail filename: %w", err)
	}

	// Download and resize image
	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to download and resize image: %w", err)
	}
	// Save thumbnail to all storage paths
	for _, basePath := range d.StoragePaths {
		// Get the directory of the original file
		dirPath := filepath.Dir(filepath.Join(basePath, task.DestPath))

		// Create the directory if it doesn't exist
		if err := os.MkdirAll(dirPath, DefaultDirPermissions); err != nil {
			return 0, 0, fmt.Errorf("failed to create directory: %w", err)
		}

		// delete old thumbnail
		if oldTnLH != 0 {
			fileNameOld, err := levelStore.Int32ToBase62(oldTnLH)
			if err != nil {
				golog.Error("failed to generate old thumbnail filename", "error", err, "oldTnLH", oldTnLH)
				return 0, 0, fmt.Errorf("failed to generate old thumbnail filename: %w", err)
			}
			filePathOld := filepath.Join(dirPath, sid+"_"+fileNameOld+".jpg")
			if _, err := os.Stat(filePathOld); err == nil {
				if err := os.Remove(filePathOld); err != nil {
					golog.Warn("failed to delete old thumbnail", "error", err, "path", filePathOld)
				}
			}
		}
		golog.Debug("generateThumbnailAndUpdate", "hash", hash, "fileName", fileName)
		// Save as JPEG in the same directory as the original file
		filePath := filepath.Join(dirPath, sid+"_"+fileName+".jpg")
		file, err := os.Create(filePath)
		if err != nil {
			return 0, 0, fmt.Errorf("failed to create thumbnail file: %w", err)
		}

		// Encode the image
		err = jpeg.Encode(file, newImg, &jpeg.Options{Quality: 100})

		// Close the file immediately after use
		closeErr := file.Close()

		// Check for encoding errors
		if err != nil {
			return 0, 0, fmt.Errorf("failed to encode thumbnail: %w", err)
		}

		// Check for close errors
		if closeErr != nil {
			golog.Error("failed to close thumbnail file", "error", closeErr, "path", filePath)
		}
	}
	return hash, tnChangedNum, nil
}

// collectPhotoStats 收集图片下载统计信息
func (d *Downloader) collectPhotoStats(result *AnalysisResult) error {
	if len(result.DownloadTasks) == 0 {
		golog.Debug("No download tasks, skipping photo stats collection")
		return nil
	}

	var totalSize int64
	var maxSize int64
	var photoCount int

	// 筛选出图片任务并收集文件大小信息
	for _, task := range result.DownloadTasks {
		if !task.IsPhoto {
			continue // 跳过非图片文件
		}

		// 获取第一个存储路径的文件大小（所有路径的文件应该相同）
		if len(d.StoragePaths) > 0 {
			fullPath := filepath.Join(d.StoragePaths[0], task.DestPath)
			if fileInfo, err := os.Stat(fullPath); err == nil {
				fileSize := fileInfo.Size()
				totalSize += fileSize
				if fileSize > maxSize {
					maxSize = fileSize
				}
				photoCount++
				golog.Debug("Photo file stats collected",
					"path", fullPath,
					"size", fileSize,
					"mediaKey", task.MediaKey)
			} else {
				golog.Warn("Failed to get file stats",
					"path", fullPath,
					"error", err,
					"mediaKey", task.MediaKey)
			}
		}
	}

	// 计算统计信息
	if photoCount > 0 {
		result.MaxPicSz = maxSize
		result.TotPicSz = totalSize
		result.AvgPicSz = totalSize / int64(photoCount)
		result.PhodlNum = photoCount
		result.PhoDl = time.Now() // 下载完成时间戳

		golog.Info("Photo download stats collected",
			"ID", result.ID,
			"photoCount", photoCount,
			"maxSize", maxSize,
			"totalSize", totalSize,
			"avgSize", result.AvgPicSz,
			"downloadTime", result.PhoDl)
	} else {
		golog.Debug("No photo files found for stats collection", "ID", result.ID)
	}

	return nil
}
