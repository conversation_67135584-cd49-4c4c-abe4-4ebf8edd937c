package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/goresodownload"
	"go.mongodb.org/mongo-driver/bson"
)

// Queue processing constants
const (
	// Worker pool settings
	WorkerChannelBuffer = 2 // Buffer size for worker channels

	// Memory and resource thresholds
	MemoryThresholdMB  = 800 // Memory threshold in MB before forcing cleanup
	GoroutineThreshold = 200 // Goroutine count threshold before forcing cleanup

	// Timing intervals
	CleanupWaitInterval = 5 * time.Second        // Wait time after cleanup
	RetryWaitInterval   = 5 * time.Second        // Wait time before retrying operations
	QueueCheckInterval  = 5 * time.Second        // Interval for checking queue when empty
	ChannelWaitInterval = 100 * time.Millisecond // Wait time when worker channel is full

	// Default priority fallback
	DefaultQueuePriority = 1000 // Default priority when calculation fails

	// Memory conversion
	BytesToMB = 1024 * 1024 // Bytes to MB conversion factor
)

// Global worker pool for queue processing
var (
	queueWorker<PERSON><PERSON> chan *goresodownload.QueueItem
	queueWorkerWg   sync.WaitGroup
	workersStarted  bool
	workersMutex    sync.Mutex
)

// Processing status tracking
var (
	processingProps sync.Map // propID -> startTime
	processingCount int64    // atomic counter for active processing

	// Detailed task status tracking for graceful shutdown
	taskStatus sync.Map // propID -> TaskStatus
)

// TaskStatus represents the current status of a task
type TaskStatus struct {
	PropID      string
	StartTime   time.Time
	Stage       string // "analyzing", "downloading", "updating_db", "removing_queue", "completed"
	LastUpdate  time.Time
}

// Task stages
const (
	StageAnalyzing    = "analyzing"
	StageDownloading  = "downloading"
	StageUpdatingDB   = "updating_db"
	StageRemovingQueue = "removing_queue"
	StageCompleted    = "completed"
)

// Processing status tracking functions
func startProcessingProp(propID string) {
	atomic.AddInt64(&processingCount, 1)
	processingProps.Store(propID, time.Now())

	// Initialize detailed task status
	status := &TaskStatus{
		PropID:     propID,
		StartTime:  time.Now(),
		Stage:      StageAnalyzing,
		LastUpdate: time.Now(),
	}
	taskStatus.Store(propID, status)

	golog.Info("Started processing prop", "propID", propID, "activeCount", atomic.LoadInt64(&processingCount), "stage", StageAnalyzing)
}

func updateTaskStage(propID string, stage string) {
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		status.Stage = stage
		status.LastUpdate = time.Now()
		taskStatus.Store(propID, status)
		golog.Info("Updated task stage", "propID", propID, "stage", stage)
	}
}

func finishProcessingProp(propID string) {
	if _, exists := processingProps.LoadAndDelete(propID); exists {
		atomic.AddInt64(&processingCount, -1)

		// Mark task as completed
		updateTaskStage(propID, StageCompleted)

		golog.Info("Finished processing prop", "propID", propID, "activeCount", atomic.LoadInt64(&processingCount))

		// Clean up task status after a delay to allow graceful shutdown to see completion
		go func() {
			time.Sleep(5 * time.Second)
			taskStatus.Delete(propID)
		}()
	}
}

func getProcessingStatus() (int64, []string) {
	count := atomic.LoadInt64(&processingCount)
	var props []string

	// Get detailed status from taskStatus map
	taskStatus.Range(func(key, value interface{}) bool {
		propID := key.(string)
		status := value.(*TaskStatus)
		duration := time.Since(status.StartTime).Seconds()
		props = append(props, fmt.Sprintf("%s[%s](%.1fs)", propID, status.Stage, duration))
		return true
	})

	// Fallback to basic status if detailed status is not available
	if len(props) == 0 {
		processingProps.Range(func(key, value interface{}) bool {
			propID := key.(string)
			startTime := value.(time.Time)
			props = append(props, fmt.Sprintf("%s(%.1fs)", propID, time.Since(startTime).Seconds()))
			return true
		})
	}

	return count, props
}

func isAllProcessingComplete() bool {
	return atomic.LoadInt64(&processingCount) == 0
}

// isAllTasksFullyComplete checks if all tasks have completed all stages including DB updates
func isAllTasksFullyComplete() bool {
	if atomic.LoadInt64(&processingCount) > 0 {
		return false
	}

	// Check if any tasks are still in non-completed stages
	allCompleted := true
	taskStatus.Range(func(key, value interface{}) bool {
		status := value.(*TaskStatus)
		if status.Stage != StageCompleted {
			allCompleted = false
			return false // Stop iteration
		}
		return true
	})

	return allCompleted
}

// ProcessQueueItem processes a single queue item (the actual analysis and download logic)
func ProcessQueueItem(queueItem *goresodownload.QueueItem) error {
	startTime := time.Now()

	// Track processing start
	startProcessingProp(queueItem.ID)
	defer finishProcessingProp(queueItem.ID)

	golog.Info("Processing queue item", "propId", queueItem.ID, "priority", queueItem.Priority)

	// Add panic recovery
	defer func() {
		if r := recover(); r != nil {
			golog.Error("Panic in ProcessQueueItem", "propId", queueItem.ID, "panic", r)
		}
	}()

	newProp, err := goresodownload.GetNewPropFromWatchTable(queueItem.ID, gBoardType)
	if err != nil {
		golog.Error("Failed to get change doc from watch table", "propId", queueItem.ID, "error", err)
		return fmt.Errorf("failed to get change doc from watch table: %w", err)
	}

	// Analyze changes
	result, err := analyzer.Analyze(newProp, gBoardType)
	if err != nil {
		speedMeter.Check("analyzeError", 1)
		return fmt.Errorf("failed to analyze changes: %w", err)
	}

	// Update stage to downloading
	updateTaskStage(queueItem.ID, StageDownloading)

	// Process analysis result (downloads and deletions)
	tnChangedNum, err := downloader.ProcessAnalysisResult(&result, gBoardType)
	if err != nil {
		speedMeter.Check("downloadError", 1)
		return fmt.Errorf("failed to process analysis result: %w", err)
	}

	// Update stage to updating database
	updateTaskStage(queueItem.ID, StageUpdatingDB)

	// Update dirStore stats
	if err := updateDirStoreStats(result, dirStore, tnChangedNum); err != nil {
		speedMeter.Check("updateDirStoreError", 1)
		return fmt.Errorf("failed to update dirStore stats: %w", err)
	}

	golog.Info("Successfully processed queue item",
		"propId", queueItem.ID,
		"downloadTasks", len(result.DownloadTasks),
		"deleteTasks", len(result.DeleteTasks),
		"duration", time.Since(startTime))
	speedMeter.Check("prop", 1)
	speedMeter.Check("downloadMedia", float64(len(result.DownloadTasks)))
	speedMeter.Check("deleteMedia", float64(len(result.DeleteTasks)))
	return nil
}

// startQueueWorkers starts fixed number of worker goroutines
func startQueueWorkers(ctx context.Context, numWorkers int) {
	workersMutex.Lock()
	defer workersMutex.Unlock()

	// Prevent multiple calls - only start workers once
	if workersStarted {
		golog.Warn("Queue workers already started, ignoring duplicate call", "numWorkers", numWorkers)
		return
	}

	queueWorkerChan = make(chan *goresodownload.QueueItem, WorkerChannelBuffer)
	workersStarted = true

	golog.Info("Starting queue workers", "numWorkers", numWorkers)

	for i := 0; i < numWorkers; i++ {
		queueWorkerWg.Add(1)
		go func(workerID int) {
			defer queueWorkerWg.Done()
			golog.Info("Queue worker started", "workerID", workerID)

			for {
				select {
				case <-ctx.Done():
					golog.Info("Queue worker stopping", "workerID", workerID)
					return
				case item, ok := <-queueWorkerChan:
					if !ok {
						golog.Info("Queue worker channel closed", "workerID", workerID)
						return
					}

					// Process the item with panic recovery
					func() {
						defer func() {
							if r := recover(); r != nil {
								golog.Error("Panic in queue worker", "workerID", workerID, "propId", item.ID, "panic", r)
							}
						}()

						golog.Info("Processing item from queue", "workerID", workerID, "propId", item.ID, "priority", item.Priority)

						// Process the item directly in the worker goroutine (no additional goroutine!)
						golog.Info("Processing queue item", "workerID", workerID, "propId", item.ID)

						err := ProcessQueueItem(item)
						if err != nil {
							// Improved error logging with more details
							errorMsg := "unknown error"
							if err != nil {
								errorMsg = err.Error()
								if errorMsg == "" {
									errorMsg = fmt.Sprintf("empty error of type %T", err)
								}
							}
							golog.Error("Failed to process queue item",
								"workerID", workerID,
								"propId", item.ID,
								"error", err,
								"errorMsg", errorMsg,
								"errorType", fmt.Sprintf("%T", err))
						} else {
							golog.Info("Successfully processed queue item", "workerID", workerID, "propId", item.ID)
							// Note: GC is now handled intelligently by gofile.smartForceGC() during image processing
							// No need for additional GC here as it would be too frequent
						}

						// Update stage to removing from queue
						updateTaskStage(item.ID, StageRemovingQueue)

						// Remove item from queue regardless of processing result (even if processing failed)
						// This prevents infinite retries and queue buildup
						if removeErr := downloadQueue.RemoveFromQueue(item); removeErr != nil {
							golog.Error("Failed to remove processed item from queue", "workerID", workerID, "propId", item.ID, "error", removeErr)
						} else {
							golog.Info("Successfully removed item from queue", "workerID", workerID, "propId", item.ID)
						}
					}()
				}
			}
		}(i)
	}
}

// stopQueueWorkers gracefully stops all queue workers
func stopQueueWorkers() {
	workersMutex.Lock()
	defer workersMutex.Unlock()

	if !workersStarted {
		golog.Debug("Queue workers not started, nothing to stop")
		return
	}

	golog.Info("Stopping queue workers")
	if queueWorkerChan != nil {
		close(queueWorkerChan)
		queueWorkerChan = nil
	}
	queueWorkerWg.Wait()
	workersStarted = false
	golog.Info("All queue workers stopped")
}

// ProcessQueue continuously processes items from the download queue
func ProcessQueue(ctx context.Context) {
	golog.Info("Starting queue processor")

	startQueueWorkers(ctx, batchSize)

	// Ensure workers are stopped when function exits
	defer stopQueueWorkers()

	for {
		select {
		case <-ctx.Done():
			golog.Info("Queue processor stopping")
			return
		default:
			// Check memory pressure and goroutine count before processing
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			numGoroutines := runtime.NumGoroutine()

			// More lenient memory threshold and goroutine limit
			if m.Alloc > MemoryThresholdMB*BytesToMB || numGoroutines > GoroutineThreshold {
				golog.Warn("High resource usage detected, forcing cleanup",
					"currentAlloc", formatBytes(m.Alloc), "numGoroutines", numGoroutines)
				runtime.GC()                    // Force GC
				time.Sleep(CleanupWaitInterval) // Shorter wait time
				// Continue processing anyway to avoid deadlock
			}

			// Get next item from queue
			item, err := downloadQueue.GetNext(gBoardType)
			if err != nil {
				golog.Error("Failed to get next item from queue", "error", err)
				time.Sleep(RetryWaitInterval) // Wait before retrying
				continue
			}

			if item == nil {
				// No items to process, wait before checking again
				time.Sleep(QueueCheckInterval)
				continue
			}

			golog.Info("Processing item from queue", "propId", item.ID, "priority", item.Priority)

			// Send item to worker pool
			// Keep trying to send until successful or context cancelled
			sent := false
			for !sent {
				select {
				case queueWorkerChan <- item:
					// Item sent to worker successfully
					sent = true
					golog.Info("Item sent to worker", "propId", item.ID)
				case <-ctx.Done():
					golog.Info("Queue processor stopping while sending item")
					return
				default:
					// Channel is full, wait a bit and try again
					golog.Debug("Worker channel full, waiting", "propId", item.ID)
					time.Sleep(ChannelWaitInterval)
					// Continue the loop to try again
				}
			}
		}
	}
}

// updateDirStoreStats updates the directory store statistics
func updateDirStoreStats(result goresodownload.AnalysisResult, dirStore *levelStore.DirKeyStore, tnChangedNum int) error {
	l1, l2, err := levelStore.GetL1L2Separate(result.PropTs, gBoardType, result.Sid)
	if err != nil {
		return fmt.Errorf("failed to get l1 and l2: %w", err)
	}
	golog.Info("updateDirStoreStats", "l1", l1, "propTs", result.PropTs, "l2", l2, "sid", result.Sid)

	// Update stats
	mediaAmount := len(result.DownloadTasks) - len(result.DeleteTasks) + tnChangedNum
	if mediaAmount != 0 {
		dirStore.AddDirStats(l1, l2, 1, mediaAmount) // 1 means one property
	}
	golog.Info("updated dirStore stats", "l1", l1, "l2", l2, "mediaAmount", mediaAmount)

	return nil
}

// getPriority calculates the priority for a queue item
func getPriority(prop bson.M) int {
	// Extract property ID from prop
	propID, ok := prop["_id"].(string)
	if !ok {
		golog.Error("Failed to extract property ID from prop")
		return DefaultQueuePriority // fallback to default priority
	}

	// Fetch existing merged property
	existMergedProp, err := goresodownload.GetExistingMergedProperty(propID, gBoardType)
	if err != nil {
		golog.Error("Failed to fetch existing merged property", "propId", propID, "error", err)
		return DefaultQueuePriority // fallback to default priority
	}

	// Calculate priority using the new priority calculator
	priority, err := goresodownload.CalculatePriority(gBoardType, existMergedProp)
	if err != nil {
		golog.Error("Failed to calculate priority", "propId", propID, "error", err)
		return DefaultQueuePriority // fallback to default priority
	}

	golog.Debug("Calculated priority", "propId", propID, "priority", priority, "boardType", gBoardType)

	return priority
}
