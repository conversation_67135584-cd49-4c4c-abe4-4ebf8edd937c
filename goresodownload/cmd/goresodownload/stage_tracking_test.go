package main

import (
	"sync"
	"sync/atomic"
	"testing"
)

// TestTaskStageTracking tests the new task stage tracking mechanism
func TestTaskStageTracking(t *testing.T) {
	// Reset counters
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	propID := "TEST_PROP_123"

	// Test starting processing
	startProcessingProp(propID)
	
	if count := atomic.LoadInt64(&processingCount); count != 1 {
		t.<PERSON><PERSON>rf("Expected processing count 1, got %d", count)
	}

	// Check initial stage
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageAnalyzing {
			t.<PERSON><PERSON>rf("Expected stage %s, got %s", StageAnalyzing, status.Stage)
		}
	} else {
		t.Error("Task status not found")
	}

	// Test stage updates
	stages := []string{StageDownloading, StageUpdatingDB, StageRemovingQueue}
	for _, stage := range stages {
		updateTaskStage(propID, stage)
		
		if statusInterface, exists := taskStatus.Load(propID); exists {
			status := statusInterface.(*TaskStatus)
			if status.Stage != stage {
				t.E<PERSON>rf("Expected stage %s, got %s", stage, status.Stage)
			}
		} else {
			t.Error("Task status not found after update")
		}
	}

	// Test completion check - should not be complete yet
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be fully complete yet")
	}

	// Test finishing processing
	finishProcessingProp(propID)
	
	if count := atomic.LoadInt64(&processingCount); count != 0 {
		t.Errorf("Expected processing count 0, got %d", count)
	}

	// Check final stage
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageCompleted {
			t.Errorf("Expected stage %s, got %s", StageCompleted, status.Stage)
		}
	} else {
		t.Error("Task status not found after completion")
	}

	// Test completion check - should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("Tasks should be fully complete now")
	}

	t.Log("Task stage tracking test passed")
}

// TestGracefulShutdownWithStages tests graceful shutdown with stage tracking
func TestGracefulShutdownWithStages(t *testing.T) {
	// Reset counters
	atomic.StoreInt64(&processingCount, 0)
	processingProps = sync.Map{}
	taskStatus = sync.Map{}

	// Simulate multiple tasks in different stages
	tasks := []struct {
		propID string
		stage  string
	}{
		{"PROP_1", StageDownloading},
		{"PROP_2", StageUpdatingDB},
		{"PROP_3", StageRemovingQueue},
	}

	// Start all tasks
	for _, task := range tasks {
		startProcessingProp(task.propID)
		updateTaskStage(task.propID, task.stage)
	}

	// Should not be complete
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be complete with active stages")
	}

	// Complete tasks one by one
	for _, task := range tasks {
		finishProcessingProp(task.propID)
		
		// Check status
		count, props := getProcessingStatus()
		t.Logf("After completing %s: count=%d, props=%v", task.propID, count, props)
	}

	// Should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}

	t.Log("Graceful shutdown with stages test passed")
}
